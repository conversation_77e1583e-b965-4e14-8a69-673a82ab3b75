
import { Component, Input, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { OpenviduSessionComponent, StreamEvent, Session, UserModel, OpenViduLayout, OvSettings, OpenViduLayoutOptions, SessionDisconnectedEvent, Publisher, OpenVidu } from 'openvidu-angular';
import { TeleConsultService } from '../tele-consult.service';
import { ActivatedRoute } from '@angular/router';
import * as Settings from '../../config/settings';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-consulting-video',
  templateUrl: './consulting-video.component.html',
  styleUrls: ['./consulting-video.component.css']
})
export class ConsultingVideoComponent implements OnInit {
  OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;
  OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;
  mySessionId: string;
  myUserName = 'Participant' + Math.floor(Math.random() * 100);
  tokens: string[] = [];
  deviceTokens: string[] = [];
  session = false;
  ovSession: Session;
  ovLocalUsers: UserModel[];
  ovLayout: OpenViduLayout;
  ovLayoutOptions: OpenViduLayoutOptions;
  ovSettings: OvSettings;

  @Input() consultationId: any;
  @Input() participantName: any;
  @Input() closeVideoSession: any;
  videoToken = '';
  appointmentId = '';
  sessionJoined = false;
  @Output() showJoinButton: EventEmitter<boolean> = new EventEmitter();
  @ViewChild('ovSessionComponent')
  public ovSessionComponent: OpenviduSessionComponent;
  sub: any;
  videoDevices: any[] = [];
  audioDevices: any[] = [];

  constructor(
    private httpClient: HttpClient,
    private route: ActivatedRoute,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService
  ) {
    this.mySessionId = this.consultationId;
  }

  ngOnInit() {
    const OV = new OpenVidu();
    OV.getDevices().then(devices => {
      this.videoDevices = devices.filter(device => device.kind === 'videoinput');
      this.audioDevices = devices.filter(device => device.kind === 'audioinput');

      if (this.videoDevices.length > 0 && this.audioDevices.length > 0) {
        this.ovSettings = {
          chat: false,
          autopublish: true,
          toolbarButtons: {
            audio: true,
            video: true,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      } else {
        this.notificationService.error('No video or audio devices have been found. Please, connect at least one. Please refresh.');
      }

      this.joinSession();
      this.joinDeviceSession();
      this.myUserName = this.participantName;
    });
  }

  async joinSession() {
    this.teleConsultService.getVideoToken(this.consultationId).subscribe((data) => {
      const msg = data['message'];
      if (msg && msg !== undefined) {
        this.notificationService.error(msg);
      } else {
        this.videoToken = data['token'];
        this.sessionJoined = true;
        this.tokens.push(this.videoToken);
        this.session = true;
      }
    }, (err) => {
      this.notificationService.error('Please refresh page');
    });
  }

  joinDeviceSession() {
    this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe((data) => {
      this.videoToken = data['token'];
      this.sessionJoined = true;
      this.deviceTokens.push(this.videoToken);
      this.session = true;
    }, (err) => {
      console.log('ERROR:' + err);
    });
  }

  handlerSessionCreatedEvent(session: Session): void {
    session.on('streamCreated', (event: StreamEvent) => {});
    session.on('streamDestroyed', (event: StreamEvent) => {});
    session.on('sessionDisconnected', (event: SessionDisconnectedEvent) => {
      this.session = false;
      this.tokens = [];
      this.showJoinButton.emit(false);
    });

    this.myMethod();
    this.publishMultipleStreams(session);
  }

  publishMultipleStreams(session: Session) {
    const OV = new OpenVidu();

    if (this.videoDevices.length < 2) {
      this.notificationService.error('Both face and otoscope cameras not found.');
      return;
    }

    this.videoDevices.forEach((device, index) => {
      const publisher = OV.initPublisher(undefined, {
        videoSource: device.deviceId,
        publishAudio: index === 0,
        resolution: '640x480',
        insertMode: 'APPEND',
        mirror: index === 0
      });

      publisher.once('accessAllowed', () => {
        session.publish(publisher);
      });

      const container = document.getElementById('multi-video-container');
      if (container) {
        publisher.addVideoElement(document.createElement('video'));
        container.appendChild(publisher.videos[0].video);
      }
    });
  }

  handlerPublisherCreatedEvent(publisher: Publisher) {
    publisher.on('streamCreated', (e) => {});
  }

  handlerErrorEvent(event): void {
    console.log(event);
  }

  myMethod() {
    this.ovLocalUsers = this.ovSessionComponent.getLocalUsers();
    this.ovLayout = this.ovSessionComponent.getOpenviduLayout();
    this.ovLayoutOptions = this.ovSessionComponent.getOpenviduLayoutOptions();
  }

  getToken(): Promise<string> {
    return this.createSession(this.consultationId).then((sessionId) => {
      return this.createToken(sessionId);
    });
  }

  createSession(sessionId) {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ customSessionId: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options)
        .pipe(catchError((error) => {
          if (error.status === 409) {
            resolve(sessionId);
          } else {
            this.notificationService.error('OpenVidu session error');
          }
          return observableThrowError(error);
        }))
        .subscribe((response) => {
          resolve(response['id']);
        });
    });
  }

  createToken(sessionId): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ session: sessionId });
      const options = {
        headers: new HttpHeaders({
          Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET),
          'Content-Type': 'application/json',
        }),
      };
      return this.httpClient
        .post(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options)
        .pipe(catchError((error) => {
          reject(error);
          return observableThrowError(error);
        }))
        .subscribe((response) => {
          resolve(response['token']);
        });
    });
  }
}
